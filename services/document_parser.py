import pymupdf
from docx import Document
import pandas as pd
from typing import Dict, List, Any, Tuple
import re
import requests
import os
import json
import hashlib

class DocumentParser:
    def __init__(self):
        self.outline = None
        self.review_criteria = None
        self.standard_outline = {
            "1": "概述",
            "2": "项目建设背景和必要性",
            "3": "项目需求分析与预期产出",
            "4": "项目选址与要素保障",
            "5": "项目建设方案",
            "6": "项目运营方案",
            "7": "项目投融资与财务方案",
            "8": "项目影响效果分析",
            "9": "项目风险管控方案",
            "10": "研究结论及建议",
            "11": "附表"
        }
        # 缓存目录
        self.cache_dir = "data"
        self.parsed_reports_dir = os.path.join(self.cache_dir, "parsed_reports")
        self.parsed_templates_dir = os.path.join(self.cache_dir, "parsed_templates")
        self._ensure_cache_dirs()
        # 目录相关关键词
        self.toc_keywords = [
            "目录", "目　录", "CONTENTS", "Contents", "contents",
            "TABLE OF CONTENTS", "Table of Contents"
        ]
        # 目录结束标识
        self.toc_end_keywords = [
            "摘要", "前言", "引言", "第一章", "1.", "1 ", "概述"
        ]

    def _ensure_cache_dirs(self):
        """确保缓存目录存在"""
        os.makedirs(self.parsed_reports_dir, exist_ok=True)
        os.makedirs(self.parsed_templates_dir, exist_ok=True)

    def _get_file_hash(self, file_path: str) -> str:
        """获取文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return ""

    def _get_report_cache_path(self, pdf_path: str) -> str:
        """获取报告缓存文件路径"""
        filename = os.path.basename(pdf_path)
        name_without_ext = os.path.splitext(filename)[0]
        return os.path.join(self.parsed_reports_dir, f"{name_without_ext}.json")

    def _get_template_cache_path(self, template_path: str, template_type: str) -> str:
        """获取模板缓存文件路径"""
        filename = os.path.basename(template_path)
        name_without_ext = os.path.splitext(filename)[0]
        return os.path.join(self.parsed_templates_dir, f"{template_type}_{name_without_ext}.json")

    def _save_report_cache(self, pdf_path: str, report_data: Dict[str, Any]):
        """保存报告解析结果到缓存"""
        cache_path = self._get_report_cache_path(pdf_path)
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            print(f"报告解析结果已缓存到: {cache_path}")
        except Exception as e:
            print(f"保存报告缓存失败: {e}")

    def _load_report_cache(self, pdf_path: str) -> Dict[str, Any]:
        """从缓存加载报告解析结果"""
        cache_path = self._get_report_cache_path(pdf_path)
        if not os.path.exists(cache_path):
            return None

        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            # 检查文件是否有变化
            current_hash = self._get_file_hash(pdf_path)
            if cached_data.get('file_hash') == current_hash:
                print(f"从缓存加载报告解析结果: {cache_path}")
                return cached_data
            else:
                print(f"文件已变化，缓存失效: {cache_path}")
                return None
        except Exception as e:
            print(f"加载报告缓存失败: {e}")
            return None

    def _save_template_cache(self, template_path: str, template_type: str, template_data: Any):
        """保存模板解析结果到缓存"""
        cache_path = self._get_template_cache_path(template_path, template_type)
        try:
            cache_data = {
                'file_hash': self._get_file_hash(template_path),
                'template_type': template_type,
                'data': template_data,
                'cached_at': os.path.getmtime(template_path)
            }
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            print(f"模板解析结果已缓存到: {cache_path}")
        except Exception as e:
            print(f"保存模板缓存失败: {e}")

    def _load_template_cache(self, template_path: str, template_type: str) -> Any:
        """从缓存加载模板解析结果"""
        cache_path = self._get_template_cache_path(template_path, template_type)
        if not os.path.exists(cache_path):
            return None

        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            # 检查文件是否有变化
            current_hash = self._get_file_hash(template_path)
            if cached_data.get('file_hash') == current_hash:
                print(f"从缓存加载{template_type}解析结果: {cache_path}")
                return cached_data.get('data')
            else:
                print(f"模板文件已变化，缓存失效: {cache_path}")
                return None
        except Exception as e:
            print(f"加载模板缓存失败: {e}")
            return None

    def get_report_info(self, pdf_path: str) -> Dict[str, Any]:
        """获取报告的基本信息（从缓存或解析）"""
        cached_data = self._load_report_cache(pdf_path)
        if cached_data:
            return {
                'report_name': cached_data.get('report_name', ''),
                'short_name': cached_data.get('short_name', ''),
                'report_date': cached_data.get('report_date', ''),
                'size': cached_data.get('size', 0),
                'sections_count': len(cached_data.get('sections', []))
            }

        # 如果没有缓存，触发解析
        project_name, sections = self.parse_pdf(pdf_path)
        return {
            'report_name': project_name,
            'short_name': self._extract_short_name(project_name),
            'report_date': '',
            'size': 0,
            'sections_count': len(sections)
        }

    def is_chapter_title(self, line: str) -> tuple[bool, str]:
        """判断是否为章节标题"""
        line = line.strip()

        # 匹配数字编号的章节标题（更灵活的模式）
        chapter_patterns = [
            r'^(\d+)[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述
            r'^第(\d+)章[.、\s]*(.+)$',  # 第1章 概述
            r'^(\d+)[.、\s]*(.+?)\.{0,}$'  # 1概述...（去掉末尾的点）
        ]

        for pattern in chapter_patterns:
            match = re.match(pattern, line)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')

                # 检查是否与标准大纲匹配（精确匹配或包含匹配）
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"

        # 匹配纯章节标题（不带编号）- 增强版本
        for num, title in self.standard_outline.items():
            # 精确匹配
            if line == title:
                return True, f"{num} {title}"

            # 包含匹配（标题在行中）
            if title in line and len(line) <= len(title) + 10:  # 允许一些额外字符
                return True, f"{num} {title}"

            # 模糊匹配（处理可能的OCR错误）
            if self._fuzzy_match_title(line, title):
                return True, f"{num} {title}"

        # 特殊处理：检查是否包含多个关键词组合
        if self._is_compound_chapter_title(line):
            return self._match_compound_title(line)

        return False, ""

    def _fuzzy_match_title(self, line: str, title: str) -> bool:
        """模糊匹配章节标题（处理OCR错误）"""
        # 移除空格和标点符号进行比较
        line_clean = re.sub(r'[\s\.\,\:\;]', '', line)
        title_clean = re.sub(r'[\s\.\,\:\;]', '', title)

        # 如果清理后的文本匹配
        if line_clean == title_clean:
            return True

        # 如果标题是行的主要部分（占比超过70%）
        if len(title_clean) > 0 and len(line_clean) > 0:
            if title_clean in line_clean and len(title_clean) / len(line_clean) > 0.7:
                return True

        return False

    def _is_compound_chapter_title(self, line: str) -> bool:
        """检查是否是复合章节标题（包含多个关键词）"""
        keywords = ['项目', '建设', '背景', '必要性', '需求', '分析', '预期', '产出',
                   '选址', '要素', '保障', '方案', '运营', '投融资', '财务',
                   '影响', '效果', '风险', '管控', '结论', '建议', '附表']

        found_keywords = [kw for kw in keywords if kw in line]
        return len(found_keywords) >= 2  # 至少包含2个关键词

    def _match_compound_title(self, line: str) -> tuple[bool, str]:
        """匹配复合章节标题"""
        # 根据关键词组合推断章节
        if '背景' in line and '必要性' in line:
            return True, "2 项目建设背景和必要性"
        elif '需求' in line and ('分析' in line or '产出' in line):
            return True, "3 项目需求分析与预期产出"
        elif '选址' in line and ('要素' in line or '保障' in line):
            return True, "4 项目选址与要素保障"
        elif '建设' in line and '方案' in line:
            return True, "5 项目建设方案"
        elif '运营' in line and '方案' in line:
            return True, "6 项目运营方案"
        elif ('投融资' in line or '财务' in line) and '方案' in line:
            return True, "7 项目投融资与财务方案"
        elif '影响' in line and '效果' in line:
            return True, "8 项目影响效果分析"
        elif '风险' in line and '管控' in line:
            return True, "9 项目风险管控方案"
        elif ('结论' in line and '建议' in line) or ('研究' in line and '结论' in line):
            return True, "10 研究结论及建议"
        elif '附表' in line or '附件' in line or '附录' in line:
            return True, "11 附表"

        return False, ""

    def detect_toc_region(self, lines: List[str]) -> tuple[int, int]:
        """检测目录区域的开始和结束位置"""
        toc_start = -1
        toc_end = -1

        # 方法1: 查找明确的目录标识
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if any(keyword in line_clean for keyword in self.toc_keywords):
                toc_start = i
                print(f"检测到目录开始位置: 第{i+1}行 - {line_clean}")
                break

        # 方法2: 如果没找到明确标识，检查前20行是否有目录特征
        if toc_start < 0:
            toc_lines_count = 0
            for i in range(min(20, len(lines))):
                line_clean = lines[i].strip()
                if self._is_toc_line(line_clean):
                    toc_lines_count += 1

            # 如果前20行中有超过5行是目录格式，认为是目录
            if toc_lines_count >= 5:
                toc_start = 0
                print(f"根据格式特征检测到目录开始: 第1行 (目录行数: {toc_lines_count})")

        # 查找目录结束位置
        if toc_start >= 0:
            # 从目录开始位置往后查找
            search_end = min(toc_start + 100, len(lines))  # 扩大搜索范围

            for i in range(toc_start + 1, search_end):
                line_clean = lines[i].strip()

                # 检查是否是目录结束标识
                if any(keyword in line_clean for keyword in self.toc_end_keywords):
                    toc_end = i
                    print(f"检测到目录结束位置: 第{i+1}行 - {line_clean}")
                    break

                # 检查是否是正文章节开始（带有实际内容的章节标题）
                is_title, chapter_title = self.is_chapter_title(line_clean)
                if is_title:
                    # 检查后续几行是否有实际内容（不是目录格式）
                    has_content = False
                    for j in range(i + 1, min(i + 15, len(lines))):
                        next_line = lines[j].strip()
                        if len(next_line) > 20 and not self._is_toc_line(next_line):
                            has_content = True
                            break

                    if has_content:
                        toc_end = i
                        print(f"检测到正文开始位置: 第{i+1}行 - {chapter_title}")
                        break

            # 如果还没找到结束位置，使用启发式方法
            if toc_end < 0:
                # 查找连续非目录行的开始
                non_toc_count = 0
                for i in range(toc_start + 1, search_end):
                    line_clean = lines[i].strip()
                    if not self._is_toc_line(line_clean) and len(line_clean) > 10:
                        non_toc_count += 1
                        if non_toc_count >= 3:  # 连续3行非目录行
                            toc_end = i - 2
                            print(f"启发式检测到目录结束位置: 第{toc_end+1}行")
                            break
                    else:
                        non_toc_count = 0

        return toc_start, toc_end

    def _is_toc_line(self, line: str) -> bool:
        """判断是否是目录行（包含页码、点号等目录特征）"""
        line = line.strip()

        if not line:
            return False

        # 目录行特征：
        # 1. 以数字结尾（页码）
        # 2. 包含连续的点号或破折号
        # 3. 章节标题+页码的格式
        # 4. 短行且包含章节关键词

        # 检查是否以页码结尾
        if re.search(r'\d+\s*$', line):
            return True

        # 检查是否包含目录格式的点号或省略号
        if re.search(r'\.{3,}', line) or re.search(r'…{2,}', line):
            return True

        # 检查章节编号+标题+页码的格式（如：1概述..............1）
        if re.match(r'^\d+[^\d]*\.{3,}\d+\s*$', line):
            return True

        # 检查是否是简短的章节标题行（目录中的标题通常较短且包含章节关键词）
        if len(line) < 80:
            # 检查是否包含章节编号
            if re.match(r'^\d+', line):
                # 检查是否包含标准章节关键词
                for title in self.standard_outline.values():
                    if title in line:
                        return True

            # 检查是否只包含章节关键词（无其他内容）
            for title in self.standard_outline.values():
                if line == title or line.startswith(title):
                    return True

        # 检查是否是纯数字行（页码）
        if line.isdigit() and len(line) <= 3:
            return True

        return False

    def _fix_text_formatting(self, text: str) -> str:
        """修复PDF文本提取中的格式问题"""
        lines = text.split('\n')

        # 检测是否存在字符分割问题（大量单字符行）
        single_char_lines = sum(1 for line in lines if len(line.strip()) == 1)
        total_lines = len([line for line in lines if line.strip()])

        if total_lines > 0 and single_char_lines / total_lines > 0.3:  # 超过30%是单字符行
            print(f"检测到字符分割问题，单字符行比例: {single_char_lines/total_lines:.2%}")
            return self._reconstruct_text_from_chars(lines)
        else:
            # 普通的文本清理
            return self._clean_text_lines(lines)

    def _reconstruct_text_from_chars(self, lines: List[str]) -> str:
        """从字符分割的行重构文本"""
        reconstructed_lines = []
        current_line = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_line:
                    reconstructed_lines.append(''.join(current_line))
                    current_line = []
                continue

            # 如果是单个字符或很短的片段，累积到当前行
            if len(line) <= 3:
                current_line.append(line)
            else:
                # 如果是较长的行，先保存当前累积的内容，然后添加这一行
                if current_line:
                    reconstructed_lines.append(''.join(current_line))
                    current_line = []
                reconstructed_lines.append(line)

        # 处理最后的累积内容
        if current_line:
            reconstructed_lines.append(''.join(current_line))

        # 进一步处理重构的文本，智能分割章节
        final_lines = self._smart_split_chapters(reconstructed_lines)

        print(f"文本重构完成: {len(lines)} -> {len(final_lines)} 行")
        return '\n'.join(final_lines)

    def _smart_split_chapters(self, lines: List[str]) -> List[str]:
        """智能分割章节"""
        final_lines = []

        for line in lines:
            if not line.strip():
                continue

            line = line.strip()

            # 查找所有可能的章节分割点
            split_points = self._find_chapter_split_points(line)

            if split_points:
                # 按分割点分割文本
                last_pos = 0
                for pos in split_points:
                    if pos > last_pos:
                        segment = line[last_pos:pos].strip()
                        if segment and len(segment) > 5:  # 过滤太短的片段
                            final_lines.append(segment)
                    last_pos = pos

                # 添加最后一段
                if last_pos < len(line):
                    segment = line[last_pos:].strip()
                    if segment and len(segment) > 5:
                        final_lines.append(segment)
            else:
                if len(line) > 5:  # 过滤太短的行
                    final_lines.append(line)

        return final_lines

    def _find_chapter_split_points(self, text: str) -> List[int]:
        """查找章节分割点"""
        split_points = []

        # 查找数字+章节标题的模式（更精确的匹配）
        for num, title in self.standard_outline.items():
            # 查找 "数字+标题" 的模式，使用正则表达式确保精确匹配
            patterns = [
                rf'\b{num}{title}\b',
                rf'\b{num}\.{title}\b',
                rf'\b{num}\s+{title}\b',
                rf'第{num}章\s*{title}\b',
                rf'\b{num}\.?\s*{re.escape(title)}'
            ]

            for pattern in patterns:
                for match in re.finditer(pattern, text):
                    split_points.append(match.start())

        # 查找纯标题的模式（用于没有编号的章节）
        for title in self.standard_outline.values():
            # 使用正则表达式查找独立的标题
            pattern = rf'\b{re.escape(title)}\b'
            for match in re.finditer(pattern, text):
                pos = match.start()
                # 检查前后文，确保这是一个独立的标题
                if self._is_standalone_title(text, pos, title):
                    split_points.append(pos)

        # 查找小节标题模式（如1.1、2.1等）
        section_pattern = r'(\d+)\.(\d+)(?:[^\d]|$)'
        for match in re.finditer(section_pattern, text):
            split_points.append(match.start())

        # 查找编号模式（如(1)、（2）等）
        number_pattern = r'[（(]\d+[）)]'
        for match in re.finditer(number_pattern, text):
            split_points.append(match.start())

        # 去重并排序
        split_points = sorted(list(set(split_points)))

        # 过滤太近的分割点（避免过度分割）
        filtered_points = []
        min_distance = 50  # 最小距离增加到50字符
        for point in split_points:
            if not filtered_points or point - filtered_points[-1] >= min_distance:
                filtered_points.append(point)

        return filtered_points

    def _is_standalone_title(self, text: str, pos: int, title: str) -> bool:
        """检查是否是独立的标题"""
        # 检查标题前后的字符
        before_char = text[pos-1] if pos > 0 else ' '
        after_pos = pos + len(title)
        after_char = text[after_pos] if after_pos < len(text) else ' '

        # 如果前后都是空白字符、标点符号或数字，认为是独立标题
        separators = [' ', '\n', '\t', '。', '，', '：', '；', '(', ')', '（', '）', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0']

        return before_char in separators and after_char in separators

    def _clean_text_lines(self, lines: List[str]) -> str:
        """清理普通文本行"""
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if line and len(line) > 1:  # 过滤掉空行和单字符行
                cleaned_lines.append(line)
        return '\n'.join(cleaned_lines)

    def get_pdf_local(self, pdf_path: str) -> str:
        all_text = pymupdf.open("a.pdf")
        return all_text

    def get_pdf_from_url(self, pdf_path: str) -> str:
        all_text = ""
        try:
            # 调用API服务解析PDF
            with open(pdf_path, 'rb') as file:
                response = requests.post(
                    os.getenv('PDF_SERVICE_URL'),
                    files={'file': (os.path.basename(pdf_path), file)},
                    timeout=180
                )

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    all_text = result['markdown']
                else:
                    print(f"API解析失败: {result.get('error', '未知错误')}")
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"调用PDF解析API失败: {e}")
        return all_text

    def extract_report_content(self, pdf_path: str) -> Dict[str, Any]:
        """统一的报告内容提取方法，返回完整的报告数据结构"""
        # 1. 尝试从缓存加载
        cached_data = self._load_report_cache(pdf_path)
        if cached_data:
            print(f"从缓存加载报告内容: {cached_data.get('report_name', '未知')}")
            return cached_data

        # 2. 如果缓存不存在或失效，进行解析
        print(f"开始解析PDF文件: {pdf_path}")

        # 初始化章节结构，添加资质证书章节
        sections = {"资质证书": ""}
        sections.update({f"{num} {title}": "" for num, title in self.standard_outline.items()})

        project_name = ""

        # 3. 先尝试读取与PDF文件同名的.md文件
        md_path = os.path.splitext(pdf_path)[0] + '.md'
        all_text = ""

        if os.path.exists(md_path):
            print(f"找到同名markdown文件: {md_path}")
            try:
                with open(md_path, 'r', encoding='utf-8') as f:
                    all_text = f.read()
                print(f"成功读取markdown文件，内容长度: {len(all_text)} 字符")
            except Exception as e:
                print(f"读取markdown文件失败: {e}")
                all_text = ""

        # 4. 如果没有.md文件或读取失败，则调用get_pdf_from_url方法
        if not all_text.strip():
            print("未找到或无法读取markdown文件，调用PDF解析API...")
            all_text = self.get_pdf_from_url(pdf_path)

            # 5. 将获取的文本存储为同目录下的.md扩展名文件
            if all_text.strip():
                try:
                    with open(md_path, 'w', encoding='utf-8') as f:
                        f.write(all_text)
                    print(f"已将解析结果保存到: {md_path}")
                except Exception as e:
                    print(f"保存markdown文件失败: {e}")

        if not all_text.strip():
            print("PDF文本提取失败...")
            # 返回空的报告数据结构
            return {
                'file_hash': self._get_file_hash(pdf_path),
                'report_name': os.path.splitext(os.path.basename(pdf_path))[0],
                'short_name': '',
                'report_date': '',
                'size': 0,
                'sections': [
                    {
                        'section_name': section_name,
                        'section_content': content
                    }
                    for section_name, content in sections.items()
                ]
            }

        # 6. 从文本中提取项目名称
        project_name = self._extract_project_name(all_text)

        # 7. 使用新的markdown解析方法
        sections = self._parse_markdown_content(all_text, sections)

        # 8. 生成简称
        short_name = self._extract_short_name(project_name)

        # 9. 构建完整的报告数据结构
        report_data = {
            'file_hash': self._get_file_hash(pdf_path),
            'report_name': project_name,
            'short_name': short_name,
            'report_date': self._extract_report_date(all_text),
            'size': len(all_text),
            'sections': [
                {
                    'section_name': section_name,
                    'section_content': content
                }
                for section_name, content in sections.items()
            ]
        }

        # 10. 保存到缓存
        self._save_report_cache(pdf_path, report_data)
        print(f"报告解析完成并已缓存: {project_name}")

        return report_data

    def parse_pdf(self, pdf_path: str) -> Tuple[str,Dict[str, str]]:
        """解析PDF文件，提取章节内容（保持向后兼容）"""
        # 使用新的统一方法
        report_data = self.extract_report_content(pdf_path)

        # 将列表格式的sections转换为字典格式以保持向后兼容
        sections_list = report_data.get('sections', [])
        sections_dict = {}
        for section in sections_list:
            sections_dict[section['section_name']] = section['section_content']

        return report_data.get('report_name', ''), sections_dict

    def _extract_short_name(self, project_name: str) -> str:
        """从项目名称提取简称"""
        if not project_name:
            return ""

        # 提取县市名称或具体项目名
        import re

        # 匹配县市名称
        county_match = re.search(r'([^市县区]+[市县区])', project_name)
        if county_match:
            return county_match.group(1)

        # 匹配项目关键词
        project_keywords = ['项目', '工程', '建设', '中心', '基地', '园区', '平台']
        for keyword in project_keywords:
            if keyword in project_name:
                # 提取关键词前的部分作为简称
                parts = project_name.split(keyword)
                if parts[0]:
                    return parts[0].strip()[:10]  # 限制长度

        # 如果都没有匹配，返回前10个字符
        return project_name[:10]

    def _extract_report_date(self, text: str) -> str:
        """从文本中提取报告日期"""
        import re

        # 匹配日期格式
        date_patterns = [
            r'(\d{4}年\d{1,2}月)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return ""

    def _parse_markdown_content(self, markdown_text: str, sections: Dict[str, str]) -> Dict[str, str]:
        """解析markdown内容，按照新的规则提取章节"""
        lines = markdown_text.split('\n')
        current_section = None
        current_content = []
        found_overview = False  # 标记是否找到概述章节

        print(f"开始解析markdown内容，共 {len(lines)} 行")

        # 首先尝试标准的markdown格式解析
        for i, line in enumerate(lines):
            line_stripped = line.strip()

            # 检查是否是一级章节标题（一个#）
            if line_stripped.startswith('# ') and not line_stripped.startswith('## '):
                chapter_title = line_stripped[2:].strip()

                # 检查是否是概述章节
                if self._is_overview_chapter(chapter_title):
                    found_overview = True
                    # 保存当前章节内容
                    if current_section and current_content:
                        sections[current_section] = '\n'.join(current_content).strip()

                    # 开始概述章节
                    current_section = self._match_standard_chapter(chapter_title)
                    current_content = []
                    print(f"找到概述章节: {current_section}")
                    continue

                # 如果还没找到概述，将内容归类到资质证书
                if not found_overview:
                    if current_section != "资质证书":
                        # 保存当前章节内容
                        if current_section and current_content:
                            sections[current_section] = '\n'.join(current_content).strip()
                        current_section = "资质证书"
                        current_content = []
                        print("将内容归类到资质证书章节")
                else:
                    # 已找到概述后的其他章节
                    # 保存当前章节内容
                    if current_section and current_content:
                        sections[current_section] = '\n'.join(current_content).strip()

                    # 开始新章节
                    current_section = self._match_standard_chapter(chapter_title)
                    current_content = []
                    print(f"找到章节: {current_section}")
                    continue

            # 检查是否是数字格式的章节标题（如 "1 概述"）
            elif line_stripped and not line_stripped.startswith('#') and not line_stripped.startswith('!'):
                import re
                match = re.match(r'^(\d+)\s+(.+)$', line_stripped)
                if match:
                    chapter_num, title = match.groups()
                    if title in ['概述', '项目建设背景和必要性', '项目需求分析与预期产出',
                               '项目选址与要素保障', '项目建设方案', '项目运营方案',
                               '项目投融资与财务方案', '项目影响效果分析', '项目风险管控方案',
                               '研究结论及建议', '附表']:

                        # 保存当前章节内容
                        if current_section and current_content:
                            sections[current_section] = '\n'.join(current_content).strip()

                        chapter_title = f"{chapter_num} {title}"
                        current_section = chapter_title
                        current_content = []
                        found_overview = True  # 找到了标准章节
                        print(f"找到数字格式章节: {current_section}")
                        continue

            # 添加内容到当前章节
            if current_section:
                current_content.append(line)
            elif not found_overview:
                # 如果还没有找到概述，且没有当前章节，归类到资质证书
                if current_section != "资质证书":
                    current_section = "资质证书"
                    current_content = []
                current_content.append(line)

        # 保存最后一个章节的内容
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        # 如果没有找到任何标准章节，尝试使用关键词提取
        if not found_overview or len(sections) < 3:
            print("标准解析未找到足够章节，尝试关键词提取...")
            self._extract_sections_from_unstructured_content(markdown_text, sections)

        return sections

    def _extract_sections_from_unstructured_content(self, content: str, sections: Dict[str, str]) -> None:
        """从非结构化内容中提取章节"""
        print("开始从非结构化内容中提取章节...")

        # 初始化所有标准章节
        for num, title in self.standard_outline.items():
            section_key = f"{num} {title}"
            if section_key not in sections:
                sections[section_key] = ""

        # 添加资质证书章节
        if "资质证书" not in sections:
            sections["资质证书"] = ""

        # 将前面的内容归类到资质证书
        lines = content.split('\n')
        overview_found = False
        current_content = []

        for line in lines:
            line_stripped = line.strip()

            # 查找概述章节的开始
            if not overview_found and ('概述' in line_stripped or '1.1' in line_stripped):
                # 将之前的内容归类到资质证书
                if current_content:
                    sections["资质证书"] = '\n'.join(current_content).strip()
                    current_content = []
                overview_found = True
                print("找到概述章节开始位置")

            if overview_found:
                # 概述章节之后的内容，尝试智能分配
                self._smart_assign_content(line, sections)
            else:
                # 概述章节之前的内容，归类到资质证书
                current_content.append(line)

        # 如果没有找到概述，将所有内容归类到资质证书
        if not overview_found and current_content:
            sections["资质证书"] = '\n'.join(current_content).strip()

        print(f"非结构化内容提取完成，共提取到 {len([s for s in sections.values() if s.strip()])} 个有内容的章节")

    def _smart_assign_content(self, line: str, sections: Dict[str, str]) -> None:
        """智能分配内容到合适的章节"""
        line_stripped = line.strip()
        if not line_stripped or len(line_stripped) < 10:
            return

        # 关键词映射
        keyword_mapping = {
            "1 概述": ["概述", "项目概况", "项目基本情况", "建设目标", "建设地点", "投资规模"],
            "2 项目建设背景和必要性": ["建设背景", "必要性", "现状", "问题", "发展需求"],
            "3 项目需求分析与预期产出": ["需求分析", "预期产出", "负荷预测", "供电能力"],
            "4 项目选址与要素保障": ["选址", "要素保障", "建设条件", "土地", "环境"],
            "5 项目建设方案": ["建设方案", "技术方案", "工程方案", "设备选型"],
            "6 项目运营方案": ["运营方案", "运营管理", "维护", "人员配置"],
            "7 项目投融资与财务方案": ["投资估算", "财务方案", "资金筹措", "经济评价"],
            "8 项目影响效果分析": ["效益分析", "社会效益", "经济效益", "环境影响"],
            "9 项目风险管控方案": ["风险分析", "风险防范", "应急预案", "安全措施"],
            "10 研究结论及建议": ["结论", "建议", "可行性", "推荐方案"],
            "11 附表": ["附表", "附件", "清单", "明细表"]
        }

        # 计算每个章节的匹配分数
        best_section = "1 概述"  # 默认分配到概述
        best_score = 0

        for section_name, keywords in keyword_mapping.items():
            score = 0
            for keyword in keywords:
                if keyword in line_stripped:
                    score += len(keyword)

            if score > best_score:
                best_score = score
                best_section = section_name

        # 将内容添加到最匹配的章节
        if sections[best_section]:
            sections[best_section] += "\n" + line
        else:
            sections[best_section] = line

    def _is_overview_chapter(self, title: str) -> bool:
        """判断是否是概述章节"""
        overview_keywords = ['概述', '项目概况', '总体情况', '基本情况', '项目基本情况']
        title_lower = title.lower()

        for keyword in overview_keywords:
            if keyword in title:
                return True

        # 检查是否包含数字1
        if '1' in title and any(keyword in title for keyword in ['概述', '概况']):
            return True

        return False

    def _match_standard_chapter(self, title: str) -> str:
        """将章节标题匹配到标准章节"""
        # 首先尝试精确匹配
        for num, standard_title in self.standard_outline.items():
            full_title = f"{num} {standard_title}"
            if title == standard_title or title == full_title:
                return full_title

        # 然后尝试关键词匹配
        for num, standard_title in self.standard_outline.items():
            # 提取关键词进行匹配
            if self._fuzzy_match_title(title, standard_title):
                return f"{num} {standard_title}"

        # 如果没有匹配到，返回原标题（可能是资质证书等特殊章节）
        return title

    def _validate_subsection_format(self, subsection_title: str) -> bool:
        """验证二级标题格式是否符合要求（## +章节序号）"""
        import re

        # 检查是否以数字开头，如：1.1 项目概况
        pattern = r'^\d+\.\d+\s+.+'
        if re.match(pattern, subsection_title):
            return True

        # 检查其他可能的格式
        patterns = [
            r'^\d+\.\d+\.\d+\s+.+',  # 1.1.1 格式
            r'^\(\d+\)\s+.+',        # (1) 格式
            r'^（\d+）\s+.+',        # （1）格式
        ]

        for pattern in patterns:
            if re.match(pattern, subsection_title):
                return True

        return False

    def _print_parsing_summary(self, sections: Dict[str, str]) -> None:
        """输出解析结果统计"""
        print(f"\n{'='*50} 解析结果统计 {'='*50}")

        total_sections = len(sections)
        non_empty_sections = sum(1 for content in sections.values() if content.strip())

        print(f"总章节数: {total_sections}")
        print(f"有内容章节数: {non_empty_sections}")
        print(f"空章节数: {total_sections - non_empty_sections}")

        print(f"\n章节详情:")
        for section_name, content in sections.items():
            content_length = len(content.strip()) if content else 0
            status = "✓ 有内容" if content_length > 0 else "✗ 无内容"
            print(f"  {section_name}: {status} ({content_length}字符)")

            # 检查二级标题格式
            if content_length > 0:
                self._check_subsection_format(section_name, content)

    def _check_subsection_format(self, section_name: str, content: str) -> None:
        """检查章节内容中的二级标题格式"""
        lines = content.split('\n')
        subsection_count = 0
        valid_format_count = 0

        for line in lines:
            line_stripped = line.strip()
            if line_stripped.startswith('## '):
                subsection_count += 1
                subsection_title = line_stripped[3:].strip()
                if self._validate_subsection_format(subsection_title):
                    valid_format_count += 1
                else:
                    print(f"    ⚠️  格式不符合要求的二级标题: {subsection_title}")

        if subsection_count > 0:
            print(f"    二级标题: {valid_format_count}/{subsection_count} 个格式正确")

    def _extract_project_name(self, text: str) -> str:
        """从文本中提取项目名称"""
        lines = text.split('\n')
        project_name_pattern = re.compile(r'^项目名称:\s*(.*)$')

        for line in lines:
            match = project_name_pattern.match(line.strip())
            if match:
                return match.group(1).strip()

        return ""
    def _validate_chapter_start(self, lines: List[str], index: int) -> bool:
        """验证是否是真正的章节开始"""
        # 检查后续几行是否有实际内容
        content_lines = 0
        for i in range(index + 1, min(index + 15, len(lines))):
            line = lines[i].strip()
            if self._is_valid_content_line(line):
                content_lines += 1
                if content_lines >= 2:  # 至少有2行有效内容
                    return True

        return False

    def _is_valid_content_line(self, line: str) -> bool:
        """判断是否是有效的内容行"""
        line = line.strip()

        # 过滤条件
        if len(line) < 5:  # 太短的行
            return False

        if line.isdigit():  # 纯数字（页码）
            return False

        if re.match(r'^[\d\-\s]+$', line):  # 只包含数字、横线、空格
            return False

        # 常见的页眉页脚模式
        footer_patterns = [
            r'^\d+$',  # 页码
            r'^第\s*\d+\s*页',  # 第X页
            r'^\d+\s*/\s*\d+$',  # X/Y页码格式
            r'^[\d\-]+$',  # 纯数字和横线
        ]

        for pattern in footer_patterns:
            if re.match(pattern, line):
                return False

        # 检查是否是目录行
        if self._is_toc_line(line):
            return False

        return True

    def _extract_by_keywords(self, text: str, sections: Dict[str, str]):
        """根据关键词提取章节内容"""
        keywords_map = {
            "1 概述": ["概述", "项目概况", "总体情况", "基本情况"],
            "2 项目建设背景和必要性": ["建设背景", "必要性", "项目背景", "建设必要性"],
            "3 项目需求分析与预期产出": ["需求分析", "预期产出", "需求", "产出"],
            "4 项目选址与要素保障": ["选址", "要素保障", "建设条件", "选址条件"],
            "5 项目建设方案": ["建设方案", "技术方案", "工程方案", "实施方案"],
            "6 项目运营方案": ["运营方案", "运营管理", "运行方案", "管理方案"],
            "7 项目投融资与财务方案": ["投融资", "财务方案", "投资估算", "资金筹措"],
            "8 项目影响效果分析": ["影响效果", "效益分析", "社会效益", "经济效益"],
            "9 项目风险管控方案": ["风险管控", "风险分析", "风险防范", "风险评估"],
            "10 研究结论及建议": ["研究结论", "建议", "结论", "总结"],
            "11 附表": ["附表", "附件", "附录", "表格"]
        }

        # 按段落分割文本，并尝试智能分配到章节
        lines = text.split('\n')
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_paragraph:
                    paragraph_text = '\n'.join(current_paragraph)
                    self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)
                    current_paragraph = []
            else:
                if self._is_valid_content_line(line):
                    current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            paragraph_text = '\n'.join(current_paragraph)
            self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)

    def _assign_paragraph_to_section(self, paragraph: str, keywords_map: Dict[str, List[str]], sections: Dict[str, str]):
        """将段落分配到合适的章节"""
        if len(paragraph.strip()) < 30:  # 跳过太短的段落
            return

        # 首先检查段落开头是否有章节标识
        first_line = paragraph.split('\n')[0].strip()
        is_title, chapter_title = self.is_chapter_title(first_line)

        if is_title:
            # 如果段落以章节标题开始，将整个段落分配到该章节
            if sections[chapter_title]:
                sections[chapter_title] += "\n\n" + paragraph.strip()
            else:
                sections[chapter_title] = paragraph.strip()
            print(f"段落分配到 {chapter_title} (章节标题匹配)")
            return

        # 计算每个章节的匹配分数
        best_section = None
        best_score = 0

        for section_name, keywords in keywords_map.items():
            score = 0
            for keyword in keywords:
                # 关键词匹配计分
                count = paragraph.count(keyword)
                if count > 0:
                    score += len(keyword) * count  # 长关键词权重更高，出现次数也影响分数

            # 额外检查：如果段落包含章节编号
            section_num = section_name.split()[0]
            if f"{section_num}." in paragraph or f"{section_num} " in paragraph:
                score += 10  # 章节编号匹配给额外分数

            if score > best_score:
                best_score = score
                best_section = section_name

        # 如果有匹配的章节，分配段落
        if best_section and best_score > 0:
            if sections[best_section]:
                sections[best_section] += "\n\n" + paragraph.strip()
            else:
                sections[best_section] = paragraph.strip()
            print(f"段落分配到 {best_section} (匹配分数: {best_score})")
        else:
            # 如果没有明确匹配，尝试根据位置分配
            # 检查段落是否包含项目基本信息
            if any(keyword in paragraph.lower() for keyword in ['项目名称', '建设目标', '建设地点', '项目概况']):
                target_section = "1 概述"
            else:
                target_section = "1 概述"  # 默认分配到概述

            if sections[target_section]:
                sections[target_section] += "\n\n" + paragraph.strip()
            else:
                sections[target_section] = paragraph.strip()
            print(f"段落分配到默认章节: {target_section}")

    def parse_outline(self, word_path: str) -> Dict[str, str]:
        """解析Word文档中的大纲，只解析第一级章节标题，章节内容全部作为一个文本"""
        # 1. 尝试从缓存加载
        cached_data = self._load_template_cache(word_path, "outline")
        if cached_data:
            self.outline = cached_data
            return cached_data

        # 2. 如果缓存不存在或失效，进行解析
        doc = Document(word_path)
        outline = {}
        current_chapter = None
        current_content = []

        for para in doc.paragraphs:
            text = para.text.strip()
            if not text:
                continue

            # 只识别主要章节标题（1-11章）
            is_main_title, chapter_title = self._is_main_chapter_title(text)

            if is_main_title:
                # 保存上一个章节的内容
                if current_chapter and current_content:
                    outline[current_chapter] = '\n'.join(current_content)

                # 开始新章节
                current_chapter = chapter_title
                current_content = []
                print(f"找到主章节: {chapter_title}")
            elif current_chapter and text:
                # 将所有内容（包括子标题）都添加到当前章节
                current_content.append(text)

        # 保存最后一个章节的内容
        if current_chapter and current_content:
            outline[current_chapter] = '\n'.join(current_content)

        print(f"大纲解析完成，共解析到 {len(outline)} 个章节")

        # 3. 保存到缓存
        self._save_template_cache(word_path, "outline", outline)

        self.outline = outline
        return outline

    def _is_main_chapter_title(self, text: str) -> tuple[bool, str]:
        """判断是否为主要章节标题（1-11章），不包括子标题"""
        text = text.strip()

        # 匹配主要章节编号的模式（只匹配1-11的单数字）
        main_chapter_patterns = [
            r'^(\d{1,2})[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述, 10 研究结论及建议
            r'^第(\d{1,2})章[.、\s]*(.+)$',  # 第1章 概述
        ]

        for pattern in main_chapter_patterns:
            match = re.match(pattern, text)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')

                # 只处理1-11章的主要章节
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"

        # 匹配纯章节标题（不带编号）
        for num, title in self.standard_outline.items():
            if text == title or (title in text and len(text) <= len(title) + 10):
                return True, f"{num} {title}"

        return False, ""

    def _is_sub_title(self, text: str) -> bool:
        """判断是否为子标题（如1.1、1.2等），需要过滤掉"""
        text = text.strip()

        # 子标题模式
        sub_title_patterns = [
            r'^\d+\.\d+',  # 1.1, 1.2, 2.1 等
            r'^\(\d+\)',   # (1), (2) 等
            r'^（\d+）',   # （1）, （2） 等
            r'^\d+）',     # 1）, 2） 等
            r'^[a-zA-Z]\.',  # a., b., A., B. 等
        ]

        for pattern in sub_title_patterns:
            if re.match(pattern, text):
                return True

        # 如果文本很短且只包含数字和少量文字，可能是子标题
        if len(text) < 20 and re.match(r'^[\d\.\s\u4e00-\u9fa5]{1,15}$', text):
            # 检查是否包含常见的子标题关键词
            sub_keywords = ['概况', '简述', '说明', '分析', '计算', '确定', '选择']
            if any(keyword in text for keyword in sub_keywords):
                return True

        return False

    def _validate_outline_content_string(self, outline: Dict[str, str]) -> None:
        """验证大纲内容（字符串格式），确保每个章节都有足够的内容"""
        print(f"\n{'='*40} 验证大纲内容 {'='*40}")

        for chapter_title, content in outline.items():
            content_length = len(content) if content else 0
            print(f"章节 '{chapter_title}': {content_length} 字符")

            if content_length < 30:
                print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
            else:
                print(f"✓ 章节 '{chapter_title}' 内容充足")

            # 显示内容预览
            if content:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"    内容预览: {preview}")

    def _validate_outline_content(self, outline: Dict[str, Any]) -> None:
        """验证大纲内容，确保每个章节都有足够的内容"""
        print(f"\n{'='*40} 验证大纲内容 {'='*40}")

        for chapter_title, content in outline.items():
            if isinstance(content, list):
                total_chars = sum(len(item) for item in content)
                print(f"章节 '{chapter_title}': {len(content)} 个段落, {total_chars} 字符")

                if total_chars < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")
            else:
                content_length = len(str(content)) if content else 0
                print(f"章节 '{chapter_title}': {content_length} 字符")

                if content_length < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")

    def get_chapter_outline(self, outline: Dict[str, str], section_title: str) -> str:
        """根据章节标题获取对应的大纲要求"""
        if not outline:
            return "未提供大纲信息"

        # 直接匹配章节标题
        if section_title in outline:
            return outline[section_title]

        # 模糊匹配：查找包含关键词的章节
        for outline_title, outline_content in outline.items():
            # 提取章节关键词进行匹配
            section_keywords = self._extract_chapter_keywords(section_title)
            outline_keywords = self._extract_chapter_keywords(outline_title)

            # 如果有关键词匹配，返回该章节大纲
            if any(keyword in outline_keywords for keyword in section_keywords):
                return outline_content

        # 如果没有找到匹配的章节，返回所有大纲信息
        all_outline = []
        for title, content in outline.items():
            all_outline.append(f"【{title}】\n{content}")

        return "未找到对应章节的大纲要求，以下是完整大纲信息：\n" + "\n\n".join(all_outline)

    def _extract_chapter_keywords(self, title: str) -> list:
        """提取章节标题的关键词"""
        import re
        # 移除数字编号和标点符号，提取关键词
        cleaned_title = re.sub(r'^\d+\.?\s*', '', title)  # 移除开头的数字编号
        cleaned_title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', ' ', cleaned_title)  # 只保留中英文
        keywords = [word.strip() for word in cleaned_title.split() if len(word.strip()) > 1]
        return keywords

    def parse_review_guide(self, file_path: str) -> str:
        """解析审查指南文档"""
        # 1. 尝试从缓存加载
        cached_data = self._load_template_cache(file_path, "guide")
        if cached_data:
            return cached_data

        # 2. 如果缓存不存在或失效，进行解析
        try:
            doc = Document(file_path)
            guide_content = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    guide_content.append(text)

            result = '\n'.join(guide_content)
            print(f"解析审查指南完成，内容长度: {len(result)} 字符")

            # 3. 保存到缓存
            self._save_template_cache(file_path, "guide", result)

            return result

        except Exception as e:
            print(f"解析审查指南文件失败: {e}")
            return "审查指南解析失败"

    def parse_review_criteria(self, excel_path: str) -> List[Dict[str, Any]]:
        """解析Excel文件中的审查细则"""
        # 1. 尝试从缓存加载
        cached_data = self._load_template_cache(excel_path, "criteria")
        if cached_data:
            self.review_criteria = cached_data
            return cached_data

        # 2. 如果缓存不存在或失效，进行解析
        df = pd.read_excel(excel_path)
        criteria = []
        current_criterion_id = None
        criterion_counter = 0

        for index, row in df.iterrows():
            # 跳过完全空的行
            if (pd.isna(row.get('序号', '')) and
                pd.isna(row.get('审查细则', '')) and
                pd.isna(row.get('审查范畴', ''))):
                continue

            # 获取审查细则内容
            content = str(row.get('审查细则', '')).strip() if not pd.isna(row.get('审查细则', '')) else ''
            requirements = str(row.get('审查范畴', '')).strip() if not pd.isna(row.get('审查范畴', '')) else ''

            # 跳过没有实际内容的行
            if not content or content in ['nan', 'NaN', '']:
                continue

            # 处理序号列（可能是合并单元格）
            sequence_num = row.get('序号', '')
            if not pd.isna(sequence_num) and str(sequence_num).strip():
                # 如果序号不为空，更新当前审查细则ID
                current_criterion_id = str(sequence_num).strip()
                criterion_counter += 1
            else:
                # 如果序号为空（合并单元格），使用当前的审查细则ID
                # 如果还没有当前ID，使用行号
                if current_criterion_id is None:
                    criterion_counter += 1
                    current_criterion_id = str(criterion_counter)

            # 使用行号作为最终的criterion_id，确保唯一性
            final_criterion_id = f"{criterion_counter}.{index + 1}"

            criteria.append({
                'id': final_criterion_id,
                'original_id': current_criterion_id,
                'content': content,
                'category': requirements,  # 使用category字段以保持一致性
                'requirements': requirements,  # 保留原字段以兼容
                'row_index': index + 1
            })

            #print(f"解析审查细则 {len(criteria)}: ID={final_criterion_id}, 原始ID={current_criterion_id}, 内容={content[:30]}...")

        print(f"总共解析到 {len(criteria)} 个有效的审查细则")

        # 3. 保存到缓存
        self._save_template_cache(excel_path, "criteria", criteria)

        self.review_criteria = criteria
        return criteria
    def get_review_criteria(self, criterion_id) -> str:
        """根据ID获取审查细则内容"""
        # 遍历self.review_criteria，比对id属性，返回对应的细则
        for criterion in self.review_criteria:
            if criterion_id == criterion['id']:
                return criterion['content']
        return f"未找到ID为 {criterion_id} 的审查细则"